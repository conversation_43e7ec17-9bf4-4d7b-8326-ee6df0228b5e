/**
 * @file config.js - 系统统一配置文件
 * @description 整合所有配置信息，提供统一的配置管理
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

// 系统配置
const SYSTEM_CONFIG = {
    // 系统信息
    SYSTEM_INFO: {
        name: 'OTA订单处理系统',
        version: '2.0.0',
        description: '基于DeepSeek+Gemini的轻量化订单解析系统',
        lastUpdated: '2024-12-19'
    },

    // API配置
    API: {
        // GoMyHire API基础URL
        BASE_URL: 'https://staging.gomyhire.com.my/api',
        
        // DeepSeek AI API配置（主要LLM）
        DEEPSEEK: {
            // 请替换为您的实际DeepSeek API Key
            API_KEY: '***********************************',
            API_URL: 'https://api.deepseek.com/v1/chat/completions',
            MODEL_CONFIG: {
                model: 'deepseek-chat',
                temperature: 0.4,
                max_tokens: 2048,
                top_p: 1,
                frequency_penalty: 0,
                presence_penalty: 0
            },
            TIMEOUT: 15000, // 15秒超时
            MAX_RETRIES: 2,
            PRIORITY: 1
        },

        // Gemini AI API配置（后备LLM）
        GEMINI: {
            // 请替换为您的实际Gemini API Key
            API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
            API_URL: 'https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash:generateContent',
            MODEL_CONFIG: {
                temperature: 0.4,
                topK: 32,
                topP: 1,
                maxOutputTokens: 2048
            },
            TIMEOUT: 30000,
            MAX_RETRIES: 1,
            PRIORITY: 2
        },

        // Google Vision API配置（图片分析专用）
        GOOGLE_VISION: {
            API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
            API_URL: 'https://vision.googleapis.com/v1/images:annotate',
            FEATURES: {
                TEXT_DETECTION: 'TEXT_DETECTION',
                DOCUMENT_TEXT_DETECTION: 'DOCUMENT_TEXT_DETECTION',
                LABEL_DETECTION: 'LABEL_DETECTION',
                OBJECT_LOCALIZATION: 'OBJECT_LOCALIZATION'
            },
            MAX_RESULTS: 10,
            TIMEOUT: 15000
        }
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        TOKEN: 'ota_system_token',
        USER_INFO: 'ota_system_user',
        BACKEND_USERS: 'ota_backend_users',
        SUB_CATEGORIES: 'ota_sub_categories',
        CAR_TYPES: 'ota_car_types',
        LAST_LOGIN: 'ota_last_login'
    },
    
    // 默认登录信息
    DEFAULT_LOGIN: {
        email: '<EMAIL>',
        password: '1234'
    },
    
    // OTA类型配置
    OTA_TYPES: {
        'chong-dealer': {
            name: 'Chong Dealer',
            description: '重庆经销商订单处理',
            enabled: true,
            priority: 1,
            keywordPatterns: [
                'CHONG 车头',
                '收单&进单',
                '\\*京鱼\\*',
                '\\*野马\\*',
                '\\*小野马\\*',
                '\\*kenny\\*',
                '\\*迹象\\*',
                '\\*鲸鱼\\*',
                '用车地点[:：]',
                '用车时间[:：]',
                '客人姓名[:：]',
                '接送机类型[:：]',
                '结算[:：]',
                '价格[:：].*?Rm',
                '举牌',
                '机场转乘',
                '包车.*?小时'
            ],
            minimumMatches: 1,
            confidence: 0.9
        },
        'auto': {
            name: '自动识别',
            description: '根据订单内容自动识别OTA类型',
            enabled: true
        },
        'other': {
            name: '其他',
            description: '通用订单处理',
            enabled: true
        }
    },

    // 智能选择规则
    SMART_SELECTION: {
        enabled: true,
        rules: {
            carTypeByPassengerCount: {
                '1': 1, '2': 1, '3': 1, '4': 1, '5': 2,
                '6': 2, '7': 2, '8': 3, '9': 3, '10': 3
            },
            subCategoryByServiceType: {
                '接机': 1, '送机': 2, '包车': 3, '点对点': 4, '机场转乘': 5
            },
            backendUserByOta: {
                'chong_dealer': 2, 'gomyhire': 1
            }
        }
    },

    // 文件上传配置
    UPLOAD: {
        MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
        ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        MAX_FILES: 10
    },
    
    // 系统设置
    SYSTEM: {
        AUTO_SAVE_INTERVAL: 30000, // 30秒自动保存
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24小时
        RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 1000,
        MAX_LOG_ENTRIES: 1000
    },

    // 性能配置
    PERFORMANCE: {
        targets: {
            otaDetectionTime: 100,
            llmProcessingTime: 15000,
            formatConversionTime: 50,
            totalProcessingTime: 17000
        },
        monitoring: {
            enabled: true,
            logSlowRequests: true,
            slowRequestThreshold: 20000
        }
    },

    // 错误处理配置
    ERROR_HANDLING: {
        retryPolicy: {
            maxRetries: 3,
            backoffMultiplier: 2,
            initialDelay: 1000
        },
        fallbackChain: ['deepseek', 'gemini', 'fallback_parser']
    },

    // 验证配置
    VALIDATION: {
        orderValidation: {
            enabled: true,
            strictMode: false,
            requiredFields: ['customerName', 'serviceType'],
            recommendedFields: ['serviceDate', 'flightNumber', 'customerContact']
        },
        dateValidation: {
            enabled: true,
            allowPastDates: false,
            maxFutureDays: 365,
            defaultYear: 'current'
        }
    },

    // 日志配置
    LOGGING: {
        enabled: true,
        level: 'info',
        maxEntries: 1000,
        includeFields: {
            originalText: false,
            apiResponses: false,
            processingTime: true,
            errorDetails: true
        }
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SYSTEM_CONFIG;
}

// 全局配置（浏览器环境）
if (typeof window !== 'undefined') {
    window.SYSTEM_CONFIG = SYSTEM_CONFIG;
}
