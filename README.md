# OTA订单处理系统

一个智能化的OTA（Online Travel Agency）订单处理系统，支持文字和图片输入，使用AI自动处理订单信息，并通过API创建订单。

**版本**: v2.0.0  
**更新日期**: 2025-06-02  
**架构**: 模块化前端应用，支持多AI服务  

## 🎯 项目概述

本系统经过全面重构，从单一3322行巨大文件重构为清晰的模块化架构。采用严格的两级目录结构，集成DeepSeek主AI + Gemini备用AI的双LLM架构，支持Google Vision图像处理，实现了完整的OTA订单自动化处理流程。

## 功能特性

### 🔐 用户认证
- 安全的登录系统
- 持久化登录状态
- 自动会话管理

### 📝 订单输入
- **文字输入**：支持直接输入订单文本
- **图片上传**：支持拖拽上传多张图片
- **OCR识别**：自动从图片中提取订单信息

### 🤖 AI智能处理

- **多AI架构**：DeepSeek (主要) + Gemini (备用) + Google Vision三重AI系统
- **连接状态监控**：实时显示所有AI服务连接状态，支持手动检测
- **智能故障切换**：15秒超时自动从DeepSeek切换到Gemini
- **图像分析**：Google Vision API进行OCR文字提取和图像理解
- **本地+云端处理**：关键词本地检测 + LLM云端解析的混合架构
- **OTA类型识别**：自动识别Chong Dealer vs 通用OTA类型
- **智能选择**：基于订单内容自动选择车型、用户、分类
- **日期智能处理**：自动修正和时间计算
- **酒店名称标准化**：中英文自动转换
- **参考号生成**：OTA类型专用格式自动生成
- **举牌服务识别**：自动检测订单中的举牌服务需求并生成独立订单

### 📋 结果预览与编辑
- 实时预览处理结果
- 支持手动编辑修正
- 多订单批量显示
- 数据验证和格式化

### 🚀 订单创建
- 自动调用GoMyHire API
- 批量创建订单
- 实时状态反馈
- 错误处理和重试机制

## 技术栈

- **前端框架**：HTML5, CSS3, JavaScript (ES6+)
- **架构模式**：模块化双层目录结构，纯前端SPA
- **AI服务集成**：
  - DeepSeek API（主要LLM处理，自然语言理解）
  - Gemini API（备用LLM处理，故障切换）
  - Google Vision API（图像分析和OCR文字提取）
- **API集成**：GoMyHire REST API（订单管理系统）
- **本地存储**：LocalStorage（用户会话和配置）
- **样式框架**：原生CSS + 现代化响应式设计
- **开发工具**：模块化ES6+，严格的代码分离
- **部署方式**：静态文件部署，支持file://协议

## 快速开始

### 1. 环境准备

确保您有以下环境：
- 现代浏览器（Chrome, Firefox, Safari, Edge）
- 网络连接（用于API调用）

### 2. 获取API密钥

#### DeepSeek AI API Key（主要LLM）
1. 访问 [DeepSeek AI Platform](https://platform.deepseek.com)
2. 注册并登录您的账户
3. 创建新的API密钥
4. 复制API密钥

#### Gemini AI API Key（备用LLM）
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录您的Google账户
3. 创建新的API密钥
4. 复制API密钥

#### Google Vision API Key（图像处理）
1. 访问 [Google Cloud Console](https://console.cloud.google.com)
2. 启用Vision API服务
3. 创建服务账户和API密钥
4. 复制API密钥

### 3. 配置系统

1. 打开 `core/config.js` 文件
2. 找到API配置部分，更新相应的API密钥：

```javascript
const CONFIG = {
    // DeepSeek AI配置（主要LLM）
    DEEPSEEK_API_KEY: 'sk-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    DEEPSEEK_API_URL: 'https://api.deepseek.com/chat/completions',
    
    // Gemini AI配置（备用LLM）
    GEMINI_API_KEY: 'AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    
    // Google Vision API配置（图像处理）
    GOOGLE_VISION_API_KEY: 'AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    
    // GoMyHire API配置
    GOMYHIRE_API_BASE: 'https://staging.gomyhire.com.my/api',
    // ...其他配置
};
```

3. 替换相应的API密钥为您的实际密钥

### 4. 启动系统

1. 将所有文件放在同一目录下
2. 使用Web服务器打开 `index.html`
   - **方法1**：使用Python简单服务器
     ```bash
     python -m http.server 8000
     ```
   - **方法2**：使用Node.js服务器
     ```bash
     npx http-server
     ```
   - **方法3**：使用VS Code Live Server扩展

3. 在浏览器中访问 `http://localhost:8000`

## 使用指南

### 登录系统

1. 打开系统后会自动弹出登录窗口
2. 默认登录信息：
   - 邮箱：`<EMAIL>`
   - 密码：`1234`
3. 点击"登录"按钮

### 输入订单信息

#### 文字输入
1. 选择"文字输入"标签
2. 在文本框中输入订单信息，例如：
```
1.28接机：KE671 22.20抵达
1.30送机：AK378 16.20起飞

联系人：张梦媛
人数：6
车型：商务十座
酒店：Santa Grand Signature

JY
```

#### 图片上传
1. 选择"图片上传"标签
2. 点击上传区域或拖拽图片文件
3. 支持多张图片同时上传
4. 系统会自动提取图片中的文字信息

### 处理订单

1. 选择OTA类型（默认为"自动识别"）
2. 点击"处理订单"按钮
3. 系统会使用AI处理订单信息
4. 处理完成后显示结果预览

### 编辑和确认

1. 在结果预览区域查看处理结果
2. 点击"编辑结果"可手动修改
3. 点击"重新处理"可重新AI处理
4. 确认无误后点击"创建订单"

### 创建订单

1. 系统会自动调用GoMyHire API
2. 显示每个订单的创建状态
3. 成功创建的订单会显示详细信息
4. 失败的订单会显示错误原因

## 订单处理规则

### 日期处理
- 自动修正过期日期
- 智能推算未来日期
- 处理跨月跨年情况
- 结果默认为2025年

### 时间计算
- **接机**：使用航班到达时间
- **送机**：航班起飞时间减去3.5小时

### 地点处理
- **接机**：pickup = klia, drop = 酒店英文名
- **送机**：pickup = 酒店英文名, drop = klia
- 自动转换中文酒店名为英文

### 参考号生成
- 基于日期+时间+航班号+客人姓名
- 确保唯一性
- **通用格式**：`202402141422-AK5747-余欣怡`
- **Chong Dealer格式**：`202402141422-AK5747-余欣怡-CHONG`
- 自动重复检测，确保系统内唯一性

### 举牌服务处理
- **自动识别**：检测订单中的"举牌"、"接机牌"、"meet and greet"等关键词
- **智能分离**：为接机订单自动生成独立的举牌服务订单
- **客人信息提取**：自动提取客人姓名用于举牌服务
- **关联管理**：举牌服务订单与原订单通过parent_order_id关联

## 📁 项目架构

### 目录结构（双层设计）

```
/
├── index.html              # 主页面入口
├── README.md              # 项目文档
├── core/                  # 核心模块（双层设计）
│   ├── app.js            # 主应用入口（~300行，精简版）
│   ├── config.js         # 统一配置管理
│   ├── logger.js         # 日志系统
│   └── prompts.js        # AI提示词配置
├── services/              # 业务服务模块（双层设计）
│   ├── api-service.js    # GoMyHire API调用服务
│   ├── llm-service.js    # DeepSeek/Gemini LLM处理
│   ├── order-parser.js   # 订单解析与智能识别
│   └── image-service.js  # 图像处理和OCR服务
├── assets/               # 静态资源（双层设计）
│   ├── styles.css       # 主样式文件
│   └── logger.css       # 日志UI样式
├── docs/                 # 统一文档管理（双层设计）
│   ├── user-guide.md    # 用户操作指南
│   ├── api-reference.md # 完整API参考文档
│   ├── development.md   # 开发和架构说明
│   ├── bug-fixes-summary.md    # Bug修复详细记录
│   ├── refactoring-summary.md  # 重构过程文档
│   └── api-list.txt     # 原始API接口列表
└── data/                 # 测试数据（双层设计）
    └── test-orders.txt  # 订单测试样本
```

### 架构特点

1. **严格双层设计**：所有目录最多两层，避免复杂嵌套
2. **模块化分离**：每个服务职责单一，接口清晰
3. **配置集中**：统一配置管理，消除重复
4. **文档完整**：全面的文档体系，包含开发和使用指南
5. **易于维护**：清晰的代码结构，标准的命名规范
│   └── logger.css       # 日志样式文件
├── docs/                 # 统一文档
│   ├── user-guide.md    # 用户指南
│   ├── api-reference.md # API参考
│   ├── development.md   # 开发文档
│   └── api-list.txt     # 原始API列表
└── data/                 # 数据文件
    └── test-orders.txt  # 测试数据
```

## 项目文档

### 📚 文档结构

项目采用统一的文档管理，所有文档集中在docs目录：

1. **[用户指南](docs/user-guide.md)** - 系统使用说明、操作指南、常见问题
2. **[API参考](docs/api-reference.md)** - 完整的API接口文档、请求响应格式
3. **[开发文档](docs/development.md)** - 系统架构、开发规范、部署指南
4. **[API列表](docs/api-list.txt)** - 原始API接口列表和说明

## API接口说明

### GoMyHire API

#### 1. 登录接口
```
POST https://staging.gomyhire.com.my/api/login
Body: {
  "email": "<EMAIL>",
  "password": "1234"
}
```

#### 2. 获取后台用户
```
GET https://staging.gomyhire.com.my/api/backend_users?search=
Headers: {
  "Authorization": "Bearer {token}"
}
```

#### 3. 获取子分类
```
GET https://staging.gomyhire.com.my/api/sub_category?search=
Headers: {
  "Authorization": "Bearer {token}"
}
```

#### 4. 获取车型
```
GET https://staging.gomyhire.com.my/api/car_types?search=
Headers: {
  "Authorization": "Bearer {token}"
}
```

#### 5. 创建订单
```
POST https://staging.gomyhire.com.my/api/create_order
Body: {
  "sub_category_id": 1,
  "car_type_id": 1,
  "incharge_by_backend_user_id": 1,
  "ota_reference_number": "OTA20241219...",
  "customer_name": "张梦媛",
  "customer_contact": "...",
  "flight_info": "KE671",
  "pickup": "klia",
  "destination": "Santa Grand Signature",
  "date": "2025-01-28",
  "time": "22:20",
  "passenger_number": 6
}
```

### DeepSeek + Gemini AI API

**主要LLM（DeepSeek）:**
```http
POST https://api.deepseek.com/chat/completions
Headers: {
  "Authorization": "Bearer {DEEPSEEK_API_KEY}",
  "Content-Type": "application/json"
}
Body: {
  "model": "deepseek-chat",
  "messages": [...],
  "stream": false
}
```

**备用LLM（Gemini）:**
```http
POST https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={API_KEY}
Body: {
  "contents": [{
    "parts": [{
      "text": "处理提示词..."
    }]
  }]
}
```

**Google Vision API:**
```http
POST https://vision.googleapis.com/v1/images:annotate?key={API_KEY}
Body: {
  "requests": [{
    "image": {"content": "base64_image_data"},
    "features": [{"type": "TEXT_DETECTION"}]
  }]
}
```

## 故障排除

### 常见问题

#### 1. 登录失败
- 检查网络连接
- 确认API地址正确
- 验证登录凭据

#### 2. AI处理失败
- 检查Gemini API Key是否正确
- 确认API配额是否充足
- 检查网络连接

#### 3. 订单创建失败
- 检查必填字段是否完整
- 确认用户权限
- 查看API返回的错误信息

#### 4. 图片上传问题
- 检查文件格式（支持JPG, PNG, GIF, WebP）
- 确认文件大小不超过10MB
- 检查浏览器兼容性

### 调试模式

1. 打开浏览器开发者工具（F12）
2. 查看Console标签页的错误信息
3. 检查Network标签页的API请求
4. 查看Application标签页的LocalStorage数据

## 安全注意事项

1. **API密钥安全**：
   - 不要在公共代码库中暴露API密钥
   - 定期轮换API密钥
   - 使用环境变量存储敏感信息

2. **数据安全**：
   - 敏感数据不会永久存储在本地
   - 使用HTTPS进行所有API通信
   - 定期清理浏览器缓存

3. **访问控制**：
   - 确保只有授权用户可以访问系统
   - 实施适当的会话管理
   - 监控异常访问行为

## 扩展功能

### 计划中的功能
- [ ] 多语言支持
- [ ] 批量文件上传
- [ ] 订单模板管理
- [ ] 数据导出功能
- [ ] 统计报表
- [ ] 移动端适配

### 自定义扩展

1. **添加新的OTA类型**：
   - 在 `config.js` 中添加新的OTA配置
   - 创建对应的处理提示词
   - 更新UI选择器

2. **集成其他AI服务**：
   - 修改 `GeminiService` 类
   - 添加新的API配置
   - 实现相应的接口适配

3. **添加OCR功能**：
   - 集成Tesseract.js或Google Vision API
   - 实现 `ImageService.extractTextFromImage` 方法
   - 优化图片预处理

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 项目地址：https://github.com/example/ota-order-system

## 更新日志

### v2.0.0 (2025-06-02) - 重大重构版本
- **架构重构**：从单一3322行文件重构为模块化双层目录结构
- **性能优化**：代码分离，每个模块职责单一，提升维护性
- **目录简化**：消除三级目录，采用严格的两级目录结构
- **配置统一**：所有配置集中在core/config.js，消除重复配置
- **文档整合**：统一文档管理，所有文档集中在docs/目录
- **代码质量**：消除重复代码，统一错误处理模式，标准化日志记录

### v1.3.0 (2024-12-19) - 重大Bug修复版本
- **全局ID修复**：修复13个元素ID不匹配问题，确保所有UI功能正常
- **文件上传修复**：解决imageUpload/imageFile ID不匹配，修复上传功能
- **拖拽上传修复**：解决imageDropZone/uploadArea ID不匹配，修复拖拽功能
- **智能选择器修复**：添加缺失的backendUserSelect、subCategorySelect、carTypeSelect元素
- **LLM状态指示器修复**：修复状态显示元素ID匹配问题
- **事件监听器修复**：添加所有缺失按钮的事件监听器（退出、编辑、刷新、导出）
- **响应式设计优化**：改进移动端显示效果，优化CSS Grid布局

### v1.2.0 (2024-12-19) - AI状态监控增强
- **新增功能**：DeepSeek + Gemini 双AI状态指示器
  - 实时显示所有AI服务连接状态
  - 三种状态：检测中（黄色）、已连接（绿色）、连接失败（红色）
  - 点击手动检测连接状态
  - 每5分钟自动检测连接状态
  - 响应式设计，支持移动设备
  - 详细的连接状态日志记录

### v1.1.0 (2024-12-19)
- **新增功能**：GoMyHire API响应日志记录
  - 详细的API请求和响应日志
  - 可折叠的JSON数据显示
  - 响应时间统计
  - 敏感信息自动脱敏
- **新增功能**：举牌服务识别与处理
  - 自动识别举牌服务关键词
  - 智能生成独立举牌服务订单
  - 客人姓名自动提取
  - 订单关联管理
- **优化功能**：OTA参考号生成规则
  - Chong Dealer类型专用格式
  - 重复检测和唯一性保证
  - 智能后缀添加机制
- **系统优化**：日志系统完善
  - API专用日志方法
  - 增强的错误处理
  - 性能监控功能

### v1.0.0 (2024-12-19)
- 初始版本发布
- 基础订单处理功能
- AI集成和API调用
- 用户界面和交互

---

## 🌟 系统特色与优势

### 🚀 核心能力
- **三重AI架构**：DeepSeek主力 + Gemini备用 + Google Vision图像处理
- **智能故障切换**：15秒超时自动切换LLM服务，确保处理连续性
- **实时状态监控**：所有AI服务连接状态实时显示，支持手动检测
- **模块化架构**：双层目录设计，代码清晰易维护
- **零依赖部署**：纯前端应用，支持本地文件协议运行

### 🎯 智能处理能力
- **订单类型识别**：自动区分Chong Dealer和通用OTA类型
- **举牌服务分离**：智能检测并生成独立举牌服务订单
- **本地+云端混合**：关键词本地预处理 + LLM云端解析
- **智能日期处理**：自动修正过期日期，智能推算未来时间
- **多格式参考号**：根据OTA类型生成专用格式参考号

### 📊 系统稳定性
- **完整错误处理**：详细的API错误捕获和用户友好提示
- **性能监控**：API响应时间统计和详细日志记录
- **UI兼容性**：修复所有元素ID不匹配问题，确保功能正常
- **响应式设计**：支持桌面和移动端设备访问

**当前版本**: v2.0.0  
**部署状态**: 生产就绪  
**维护状态**: 积极维护  

**注意**：本系统仅用于演示和学习目的。在生产环境中使用前，请确保进行充分的测试和安全评估。