/**
 * @file llm-service.js - LLM服务管理类
 * @description 管理DeepSeek和Gemini AI服务，提供统一的LLM处理接口
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

/**
 * @class LLMService - LLM服务管理类
 * @description 管理DeepSeek（主要）和Gemini（备用）AI服务
 */
class LLMService {
    /**
     * @function constructor - 构造函数
     */
    constructor() {
        // 初始化提示词管理器
        this.promptManager = new PromptManager();

        // DeepSeek 状态
        this.deepseekStatus = {
            connectionStatus: 'checking',
            lastCheckTime: null,
            consecutiveFailures: 0,
            isChecking: false,
            lastSuccessTime: null
        };

        // Gemini 状态
        this.geminiStatus = {
            connectionStatus: 'checking',
            lastCheckTime: null,
            consecutiveFailures: 0,
            isChecking: false,
            lastSuccessTime: null
        };

        // 当前使用的 LLM
        this.currentLLM = 'deepseek'; // deepseek 或 gemini
    }

    /**
     * @function checkDeepSeekConnection - 检测DeepSeek API连接状态
     * @returns {Promise<boolean>} 连接状态
     */
    async checkDeepSeekConnection() {
        if (this.deepseekStatus.isChecking) {
            logger.debug('DeepSeek', '连接检测正在进行中，跳过重复检测');
            return this.deepseekStatus.connectionStatus === 'connected';
        }

        this.deepseekStatus.isChecking = true;
        const previousStatus = this.deepseekStatus.connectionStatus;
        this.deepseekStatus.connectionStatus = 'checking';

        logger.info('DeepSeek', '开始检测DeepSeek API连接状态', {
            previousStatus: previousStatus,
            consecutiveFailures: this.deepseekStatus.consecutiveFailures
        });

        try {
            // 检查API Key是否存在
            if (!SYSTEM_CONFIG.API.DEEPSEEK.API_KEY || 
                SYSTEM_CONFIG.API.DEEPSEEK.API_KEY === 'sk-your-deepseek-api-key-here') {
                logger.warn('DeepSeek', 'DeepSeek API Key未配置');
                this.deepseekStatus.connectionStatus = 'disconnected';
                this.deepseekStatus.consecutiveFailures++;
                return false;
            }

            // 发送测试请求
            const startTime = Date.now();
            const response = await fetch(SYSTEM_CONFIG.API.DEEPSEEK.API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${SYSTEM_CONFIG.API.DEEPSEEK.API_KEY}`
                },
                body: JSON.stringify({
                    model: SYSTEM_CONFIG.API.DEEPSEEK.MODEL_CONFIG.model,
                    messages: [{ role: 'user', content: 'ping' }],
                    max_tokens: 5,
                    temperature: 0.1
                }),
                signal: AbortSignal.timeout(10000) // 10秒超时
            });

            const responseTime = Date.now() - startTime;

            if (response.ok) {
                const wasDisconnected = previousStatus === 'disconnected';
                this.deepseekStatus.connectionStatus = 'connected';
                this.deepseekStatus.lastCheckTime = new Date();
                this.deepseekStatus.lastSuccessTime = new Date();
                this.deepseekStatus.consecutiveFailures = 0;

                logger.success('DeepSeek', 'DeepSeek API连接成功', {
                    responseTime: `${responseTime}ms`,
                    statusChanged: wasDisconnected
                });

                return true;
            } else {
                this.deepseekStatus.connectionStatus = 'disconnected';
                this.deepseekStatus.consecutiveFailures++;

                logger.error('DeepSeek', `DeepSeek API连接失败: ${response.status}`, {
                    responseTime: `${responseTime}ms`,
                    consecutiveFailures: this.deepseekStatus.consecutiveFailures
                });

                return false;
            }
        } catch (error) {
            this.deepseekStatus.connectionStatus = 'disconnected';
            this.deepseekStatus.consecutiveFailures++;

            logger.error('DeepSeek', 'DeepSeek API连接检测失败', {
                error: error.message,
                consecutiveFailures: this.deepseekStatus.consecutiveFailures
            });

            return false;
        } finally {
            this.deepseekStatus.isChecking = false;
            this.deepseekStatus.lastCheckTime = new Date();
        }
    }

    /**
     * @function checkGeminiConnection - 检测Gemini API连接状态
     * @returns {Promise<boolean>} 连接状态
     */
    async checkGeminiConnection() {
        if (this.geminiStatus.isChecking) {
            logger.debug('Gemini', '连接检测正在进行中，跳过重复检测');
            return this.geminiStatus.connectionStatus === 'connected';
        }

        this.geminiStatus.isChecking = true;
        const previousStatus = this.geminiStatus.connectionStatus;
        this.geminiStatus.connectionStatus = 'checking';

        logger.info('Gemini', '开始检测Gemini API连接状态', {
            previousStatus: previousStatus,
            consecutiveFailures: this.geminiStatus.consecutiveFailures
        });

        try {
            // 检查API Key是否存在
            if (!SYSTEM_CONFIG.API.GEMINI.API_KEY) {
                logger.warn('Gemini', 'Gemini API Key未配置');
                this.geminiStatus.connectionStatus = 'disconnected';
                this.geminiStatus.consecutiveFailures++;
                return false;
            }

            // 发送测试请求
            const startTime = Date.now();
            const response = await fetch(
                `${SYSTEM_CONFIG.API.GEMINI.API_URL}?key=${SYSTEM_CONFIG.API.GEMINI.API_KEY}`,
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: 'ping' }] }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 5
                        }
                    }),
                    signal: AbortSignal.timeout(10000) // 10秒超时
                }
            );

            const responseTime = Date.now() - startTime;

            if (response.ok) {
                const wasDisconnected = previousStatus === 'disconnected';
                this.geminiStatus.connectionStatus = 'connected';
                this.geminiStatus.lastCheckTime = new Date();
                this.geminiStatus.lastSuccessTime = new Date();
                this.geminiStatus.consecutiveFailures = 0;

                logger.success('Gemini', 'Gemini API连接成功', {
                    responseTime: `${responseTime}ms`,
                    statusChanged: wasDisconnected
                });

                return true;
            } else {
                this.geminiStatus.connectionStatus = 'disconnected';
                this.geminiStatus.consecutiveFailures++;

                logger.error('Gemini', `Gemini API连接失败: ${response.status}`, {
                    responseTime: `${responseTime}ms`,
                    consecutiveFailures: this.geminiStatus.consecutiveFailures
                });

                return false;
            }
        } catch (error) {
            this.geminiStatus.connectionStatus = 'disconnected';
            this.geminiStatus.consecutiveFailures++;

            logger.error('Gemini', 'Gemini API连接检测失败', {
                error: error.message,
                consecutiveFailures: this.geminiStatus.consecutiveFailures
            });

            return false;
        } finally {
            this.geminiStatus.isChecking = false;
            this.geminiStatus.lastCheckTime = new Date();
        }
    }

    /**
     * @function processOrderText - 处理订单文本
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Promise<object>} 处理结果
     */
    async processOrderText(text, otaType = 'auto') {
        const startTime = Date.now();
        
        logger.info('LLM', '开始处理订单文本', {
            textLength: text.length,
            otaType: otaType
        });

        try {
            // 1. 首先尝试DeepSeek（15秒超时）
            const deepSeekResult = await this.callDeepSeek(text, otaType);
            
            if (deepSeekResult.success) {
                logger.success('LLM', 'DeepSeek处理成功', {
                    processingTime: Date.now() - startTime,
                    provider: 'deepseek'
                });
                
                return {
                    success: true,
                    data: deepSeekResult.data,
                    provider: 'deepseek',
                    processingTime: Date.now() - startTime
                };
            }

            // 2. DeepSeek失败，尝试Gemini备用
            logger.warn('LLM', 'DeepSeek处理失败，切换到Gemini', {
                deepSeekError: deepSeekResult.error
            });

            const geminiResult = await this.callGemini(text, otaType);
            
            if (geminiResult.success) {
                logger.success('LLM', 'Gemini处理成功', {
                    processingTime: Date.now() - startTime,
                    provider: 'gemini'
                });
                
                return {
                    success: true,
                    data: geminiResult.data,
                    provider: 'gemini',
                    processingTime: Date.now() - startTime
                };
            }

            // 3. 两个LLM都失败
            logger.error('LLM', '所有LLM处理失败', {
                deepSeekError: deepSeekResult.error,
                geminiError: geminiResult.error
            });

            return {
                success: false,
                error: 'DeepSeek和Gemini处理均失败',
                processingTime: Date.now() - startTime
            };

        } catch (error) {
            logger.error('LLM', 'LLM处理异常', { error: error.message });
            return {
                success: false,
                error: error.message,
                processingTime: Date.now() - startTime
            };
        }
    }

    /**
     * @function callDeepSeek - 调用DeepSeek API
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Promise<object>} 处理结果
     */
    async callDeepSeek(text, otaType) {
        try {
            const prompt = this.promptManager.getOTAPrompt(otaType, text, new Date().toISOString().split('T')[0]);
            
            const response = await fetch(SYSTEM_CONFIG.API.DEEPSEEK.API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${SYSTEM_CONFIG.API.DEEPSEEK.API_KEY}`
                },
                body: JSON.stringify({
                    model: SYSTEM_CONFIG.API.DEEPSEEK.MODEL_CONFIG.model,
                    messages: [{ role: 'user', content: prompt }],
                    ...SYSTEM_CONFIG.API.DEEPSEEK.MODEL_CONFIG
                }),
                signal: AbortSignal.timeout(SYSTEM_CONFIG.API.DEEPSEEK.TIMEOUT)
            });

            if (!response.ok) {
                throw new Error(`DeepSeek API请求失败: ${response.status}`);
            }

            const data = await response.json();
            const content = data.choices?.[0]?.message?.content || '';

            return {
                success: true,
                data: this.parseResponse(content)
            };

        } catch (error) {
            return {
                success: false,
                error: `DeepSeek调用失败: ${error.message}`
            };
        }
    }

    /**
     * @function callGemini - 调用Gemini API
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Promise<object>} 处理结果
     */
    async callGemini(text, otaType) {
        try {
            const prompt = this.promptManager.getOTAPrompt(otaType, text, new Date().toISOString().split('T')[0]);
            
            const response = await fetch(
                `${SYSTEM_CONFIG.API.GEMINI.API_URL}?key=${SYSTEM_CONFIG.API.GEMINI.API_KEY}`,
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: prompt }] }],
                        generationConfig: SYSTEM_CONFIG.API.GEMINI.MODEL_CONFIG
                    }),
                    signal: AbortSignal.timeout(SYSTEM_CONFIG.API.GEMINI.TIMEOUT)
                }
            );

            if (!response.ok) {
                throw new Error(`Gemini API请求失败: ${response.status}`);
            }

            const data = await response.json();
            const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';

            return {
                success: true,
                data: this.parseResponse(content)
            };

        } catch (error) {
            return {
                success: false,
                error: `Gemini调用失败: ${error.message}`
            };
        }
    }

    /**
     * @function parseResponse - 解析LLM响应
     * @param {string} content - LLM响应内容
     * @returns {object} 解析后的数据
     */
    parseResponse(content) {
        // 这里实现响应解析逻辑
        // 根据项目需求解析LLM返回的订单信息
        return {
            rawContent: content,
            orders: [], // 解析后的订单列表
            metadata: {} // 元数据
        };
    }

    /**
     * @function getStatus - 获取LLM服务状态
     * @returns {object} 服务状态
     */
    getStatus() {
        return {
            deepseek: this.deepseekStatus,
            gemini: this.geminiStatus,
            currentLLM: this.currentLLM
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LLMService;
} else if (typeof window !== 'undefined') {
    window.LLMService = LLMService;
}
